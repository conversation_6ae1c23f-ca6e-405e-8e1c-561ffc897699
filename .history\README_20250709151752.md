# 通用命令并行执行器

一个强大的Python工具，用于并行执行任意命令（包括Python脚本、系统命令等），支持日志记录和超时控制。

## 功能特性

- ✅ **并行执行**: 同时运行多个命令（Python脚本、系统命令等）
- ✅ **通用命令支持**: 支持任意命令，不仅限于Python脚本
- ✅ **日志记录**: 每个命令的输出单独记录到指定目录
- ✅ **超时控制**: 设定最大执行时间，超时自动终止
- ✅ **实时输出**: 实时显示每个命令的执行状态
- ✅ **错误处理**: 完善的错误处理和异常捕获
- ✅ **执行报告**: 详细的执行结果统计
- ✅ **多种输入方式**: 支持命令行参数、文件输入等

## 快速开始

### 方法1: 通用命令执行（推荐）

```python
from parallel_python_executor import execute_commands_parallel

# 定义要执行的命令（支持任意命令）
commands = [
    ("python", ["psis_download_firmware_tool.py", "***************", "5", "1.1", "r12"]),
    ("ping", ["-n", "3", "***************"]),
    ("echo", ["Hello from parallel executor"])
]

# 执行命令
result = execute_commands_parallel(
    commands=commands,
    log_dir="./logs",
    timeout=600  # 10分钟超时
)

# 检查结果
if result['success']:
    print("所有命令执行成功!")
else:
    print(f"有 {result['failed_count']} 个命令执行失败")
```

### 方法2: Python脚本执行（向后兼容）

```python
from parallel_python_executor import execute_python_scripts_parallel

# 定义要执行的Python脚本
scripts = [
    ("psis_download_firmware_tool.py", ["***************", "5", "1.1", "r12"]),
    ("psis_download_firmware_tool.py", ["***************", "8", "1.1", "r12"]),
    ("psis_download_firmware_tool.py", ["***************", "1", "1.1", "r12"])
]

# 执行脚本（自动添加python前缀）
result = execute_python_scripts_parallel(
    scripts=scripts,
    log_dir="./logs",
    timeout=600
)
```

### 方法3: 使用类的方式（高级用法）

```python
from parallel_python_executor import CommandExecutor

# 创建执行器实例
executor = CommandExecutor(
    log_dir="./logs",
    timeout=300
)

# 执行命令
result = executor.execute_commands(commands)
```

## 参数说明

### execute_commands_parallel函数（推荐）

- `commands`: 命令列表，每个元素是 `(命令, 参数列表)` 的元组
- `log_dir`: 日志目录路径
- `timeout`: 超时时间（秒），默认300秒

### execute_python_scripts_parallel函数（向后兼容）

- `scripts`: 脚本列表，每个元素是 `(脚本路径, 参数列表)` 的元组
- `log_dir`: 日志目录路径
- `timeout`: 超时时间（秒），默认300秒

### 返回结果

函数返回一个包含以下信息的字典：

```python
{
    'success': bool,           # 是否所有脚本都成功执行
    'total_scripts': int,      # 总脚本数
    'success_count': int,      # 成功执行的脚本数
    'failed_count': int,       # 失败的脚本数
    'total_time': float,       # 总执行时间（秒）
    'timeout_occurred': bool,  # 是否发生超时
    'results': dict,           # 每个脚本的详细结果
    'log_directory': str       # 日志目录路径
}
```

## 日志文件

每个脚本的日志文件包含：
- 执行时间和命令
- 实时输出内容
- 返回码和结束时间
- 错误信息（如果有）

日志文件命名格式：`脚本名_参数_时间戳.log`

## 示例

### 示例1: 固件下载脚本

```python
scripts = [
    ("psis_download_firmware_tool.py", ["***************", "5", "1.1", "r12"]),
    ("psis_download_firmware_tool.py", ["***************", "8", "1.1", "r12"]),
    ("psis_download_firmware_tool.py", ["***************", "1", "1.1", "r12"])
]

result = execute_python_scripts_parallel(scripts, "./logs/firmware", 600)
```

### 示例2: 数据处理脚本

```python
scripts = [
    ("process_data.py", ["--input", "data1.csv", "--output", "result1.csv"]),
    ("process_data.py", ["--input", "data2.csv", "--output", "result2.csv"]),
    ("analyze_data.py", ["--verbose", "--report", "analysis.txt"])
]

result = execute_python_scripts_parallel(scripts, "./logs/data_processing", 300)
```

## 命令行工具

提供了便捷的命令行工具 `parallel_runner.py`：

### 基本用法

```bash
# 直接指定脚本命令
python parallel_runner.py -s "script1.py arg1 arg2" -s "script2.py arg3" -l ./logs -t 300

# 从文件读取脚本列表
python parallel_runner.py -f scripts.txt -l ./logs -t 300 -v

# JSON格式输出
python parallel_runner.py -s "test_script.py task1" --json
```

### 固件下载示例

```bash
python parallel_runner.py \
  -s "psis_download_firmware_tool.py *************** 5 1.1 r12" \
  -s "psis_download_firmware_tool.py *************** 8 1.1 r12" \
  -s "psis_download_firmware_tool.py *************** 1 1.1 r12" \
  -l ./firmware_logs -t 600 -v
```

### 脚本文件格式

创建 `scripts.txt` 文件：
```
# 注释行以#开头
psis_download_firmware_tool.py *************** 5 1.1 r12
psis_download_firmware_tool.py *************** 8 1.1 r12
psis_download_firmware_tool.py *************** 1 1.1 r12
```

## 运行演示

```bash
# 运行演示程序
python demo.py

# 查看使用示例
python example_usage.py

# 固件下载示例
python firmware_download_example.py
```

## 文件说明

- `parallel_python_executor.py`: 主要的执行器类和函数
- `example_usage.py`: 详细的使用示例
- `demo.py`: 简单的演示程序
- `test_script.py`: 用于测试的示例脚本
- `README.md`: 本说明文件

## 注意事项

1. **脚本路径**: 确保要执行的Python脚本存在且可执行
2. **权限**: 确保有足够的权限创建日志目录和文件
3. **资源**: 并行执行多个脚本会消耗更多系统资源
4. **超时**: 合理设置超时时间，避免脚本无限期运行
5. **日志**: 日志文件会实时写入，可以在执行过程中查看

## 错误处理

- 脚本执行失败会记录到日志文件
- 超时会自动终止所有剩余进程
- 异常情况会在控制台和日志中记录

## 依赖

- Python 3.6+
- 标准库: `subprocess`, `threading`, `pathlib`, `datetime`
