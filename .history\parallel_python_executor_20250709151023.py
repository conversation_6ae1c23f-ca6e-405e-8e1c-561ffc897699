#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用的Python脚本并行执行器
支持并行执行多个Python脚本，记录日志，设置超时时间
"""

import os
import sys
import subprocess
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Tuple


class CommandExecutor:
    """通用命令并行执行器"""

    def __init__(self, log_dir: str, timeout: int = 300):
        """
        初始化执行器

        Args:
            log_dir: 日志目录路径
            timeout: 超时时间（秒），默认300秒
        """
        self.log_dir = Path(log_dir)
        self.timeout = timeout
        self.processes = {}
        self.threads = {}
        self.results = {}
        self.start_time = None
        
        # 创建日志目录
        self.log_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_log_filename(self, script_path: str, args: List[str]) -> str:
        """
        生成日志文件名
        
        Args:
            script_path: 脚本路径
            args: 脚本参数
            
        Returns:
            日志文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        script_name = Path(script_path).stem
        
        # 如果有参数，将前几个参数加入文件名
        if args:
            # 取前3个参数，避免文件名过长
            arg_str = "_".join(str(arg).replace(".", "_").replace("/", "_") for arg in args[:3])
            return f"{script_name}_{arg_str}_{timestamp}.log"
        else:
            return f"{script_name}_{timestamp}.log"
    
    def _execute_script(self, script_path: str, args: List[str], log_file: Path, thread_id: str):
        """
        执行单个脚本的线程函数
        
        Args:
            script_path: Python脚本路径
            args: 脚本参数列表
            log_file: 日志文件路径
            thread_id: 线程ID
        """
        try:
            # 构建命令
            cmd = ["python", script_path] + args
            
            print(f"[{thread_id}] 开始执行: {' '.join(cmd)}")
            
            # 启动进程
            with open(log_file, 'w', encoding='utf-8') as f:
                # 写入执行信息
                f.write(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"执行命令: {' '.join(cmd)}\n")
                f.write(f"工作目录: {os.getcwd()}\n")
                f.write("-" * 50 + "\n")
                f.flush()
                
                # 启动进程，合并stdout和stderr
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1
                )
                
                self.processes[thread_id] = process
                
                # 实时读取输出并写入日志
                for line in iter(process.stdout.readline, ''):
                    f.write(line)
                    f.flush()
                    print(f"[{thread_id}] {line.rstrip()}")
                
                # 等待进程结束
                return_code = process.wait()
                
                # 记录结束信息
                end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write("-" * 50 + "\n")
                f.write(f"执行结束时间: {end_time}\n")
                f.write(f"返回码: {return_code}\n")
                
                self.results[thread_id] = {
                    'return_code': return_code,
                    'log_file': str(log_file),
                    'command': ' '.join(cmd),
                    'success': return_code == 0
                }
                
                if return_code == 0:
                    print(f"[{thread_id}] 执行成功")
                else:
                    print(f"[{thread_id}] 执行失败，返回码: {return_code}")
                    
        except Exception as e:
            error_msg = f"执行脚本时发生错误: {str(e)}"
            print(f"[{thread_id}] {error_msg}")
            
            # 记录错误到日志文件
            try:
                with open(log_file, 'a', encoding='utf-8') as f:
                    f.write(f"\n错误信息: {error_msg}\n")
            except:
                pass
                
            self.results[thread_id] = {
                'return_code': -1,
                'log_file': str(log_file),
                'command': ' '.join(["python", script_path] + args),
                'success': False,
                'error': error_msg
            }
    
    def execute_scripts(self, scripts: List[Tuple[str, List[str]]]) -> Dict:
        """
        并行执行多个Python脚本
        
        Args:
            scripts: 脚本列表，每个元素是(脚本路径, 参数列表)的元组
            
        Returns:
            执行结果字典
        """
        if not scripts:
            return {'success': False, 'message': '没有要执行的脚本'}
        
        self.start_time = time.time()
        print(f"开始并行执行 {len(scripts)} 个脚本，超时时间: {self.timeout}秒")
        
        # 为每个脚本创建线程
        for i, (script_path, args) in enumerate(scripts):
            thread_id = f"thread_{i+1}"
            log_filename = self._get_log_filename(script_path, args)
            log_file = self.log_dir / log_filename
            
            # 创建并启动线程
            thread = threading.Thread(
                target=self._execute_script,
                args=(script_path, args, log_file, thread_id),
                name=thread_id
            )
            thread.daemon = True
            self.threads[thread_id] = thread
            thread.start()
        
        # 等待所有线程完成或超时
        all_completed = False
        while not all_completed and (time.time() - self.start_time) < self.timeout:
            all_completed = True
            for thread_id, thread in self.threads.items():
                if thread.is_alive():
                    all_completed = False
                    break
            
            if not all_completed:
                time.sleep(1)  # 每秒检查一次
        
        # 处理超时情况
        if not all_completed:
            print(f"执行超时({self.timeout}秒)，正在终止剩余进程...")
            self._terminate_remaining_processes()
        
        # 等待所有线程结束（最多再等5秒）
        for thread in self.threads.values():
            thread.join(timeout=5)
        
        # 生成执行报告
        return self._generate_report()
    
    def _terminate_remaining_processes(self):
        """终止剩余的进程"""
        for thread_id, process in self.processes.items():
            if process and process.poll() is None:  # 进程仍在运行
                try:
                    process.terminate()
                    print(f"[{thread_id}] 进程已终止")
                    
                    # 如果进程没有在5秒内终止，强制杀死
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                        print(f"[{thread_id}] 进程已强制杀死")
                        
                except Exception as e:
                    print(f"[{thread_id}] 终止进程时发生错误: {e}")
    
    def _generate_report(self) -> Dict:
        """生成执行报告"""
        total_time = time.time() - self.start_time
        success_count = sum(1 for result in self.results.values() if result.get('success', False))
        total_count = len(self.results)
        
        report = {
            'success': success_count == total_count and total_count > 0,
            'total_scripts': total_count,
            'success_count': success_count,
            'failed_count': total_count - success_count,
            'total_time': round(total_time, 2),
            'timeout_occurred': total_time >= self.timeout,
            'results': self.results,
            'log_directory': str(self.log_dir)
        }
        
        return report


def execute_python_scripts_parallel(scripts: List[Tuple[str, List[str]]], 
                                   log_dir: str, 
                                   timeout: int = 300) -> Dict:
    """
    并行执行多个Python脚本的便捷函数
    
    Args:
        scripts: 脚本列表，每个元素是(脚本路径, 参数列表)的元组
        log_dir: 日志目录路径
        timeout: 超时时间（秒），默认300秒
        
    Returns:
        执行结果字典
        
    Example:
        scripts = [
            ("psis_download_firmware_tool.py", ["***************", "5", "1.1", "r12"]),
            ("psis_download_firmware_tool.py", ["***************", "8", "1.1", "r12"]),
            ("psis_download_firmware_tool.py", ["***************", "1", "1.1", "r12"])
        ]
        result = execute_python_scripts_parallel(scripts, "./logs", 600)
    """
    executor = PythonScriptExecutor(log_dir, timeout)
    return executor.execute_scripts(scripts)


if __name__ == "__main__":
    # 示例用法
    scripts = [
        ("psis_download_firmware_tool.py", ["***************", "5", "1.1", "r12"]),
        ("psis_download_firmware_tool.py", ["***************", "8", "1.1", "r12"]),
        ("psis_download_firmware_tool.py", ["***************", "1", "1.1", "r12"])
    ]
    
    result = execute_python_scripts_parallel(scripts, "./logs", 600)
    
    print("\n" + "="*50)
    print("执行报告:")
    print(f"总脚本数: {result['total_scripts']}")
    print(f"成功数: {result['success_count']}")
    print(f"失败数: {result['failed_count']}")
    print(f"总耗时: {result['total_time']}秒")
    print(f"是否超时: {'是' if result['timeout_occurred'] else '否'}")
    print(f"日志目录: {result['log_directory']}")
    
    if not result['success']:
        print("\n失败的脚本:")
        for thread_id, res in result['results'].items():
            if not res['success']:
                print(f"  {thread_id}: {res['command']}")
                if 'error' in res:
                    print(f"    错误: {res['error']}")
