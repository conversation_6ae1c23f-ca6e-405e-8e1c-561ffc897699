#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行Python脚本执行器 - 命令行工具
提供简单的命令行接口来并行执行Python脚本
"""

import argparse
import sys
import os
import json
from datetime import datetime
from parallel_python_executor import execute_commands_parallel, execute_python_scripts_parallel


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="并行执行多个命令",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本用法 - 任意命令
  python parallel_runner.py -c "ping -c 3 google.com" -c "curl -I https://github.com" -l ./logs -t 300

  # Python脚本（自动添加python前缀）
  python parallel_runner.py -s "script1.py arg1 arg2" -s "script2.py arg3" -l ./logs -t 300

  # 混合命令类型
  python parallel_runner.py \\
    -c "python psis_download_firmware_tool.py *************** 5 1.1 r12" \\
    -c "ping -c 4 ***************" \\
    -s "test_script.py task1" \\
    -l ./logs -t 600

  # 从文件读取命令列表
  python parallel_runner.py -f commands.txt -l ./logs -t 300
        """
    )
    
    # 命令参数
    command_group = parser.add_mutually_exclusive_group(required=True)
    command_group.add_argument(
        '-c', '--command',
        action='append',
        dest='commands',
        help='要执行的命令和参数 (可以多次使用)'
    )
    command_group.add_argument(
        '-s', '--script',
        action='append',
        dest='scripts',
        help='要执行的Python脚本和参数，自动添加python前缀 (可以多次使用)'
    )
    command_group.add_argument(
        '-f', '--file',
        dest='command_file',
        help='包含命令列表的文件路径 (每行一个命令)'
    )
    
    # 其他参数
    parser.add_argument(
        '-l', '--log-dir',
        dest='log_dir',
        default='./logs',
        help='日志目录路径 (默认: ./logs)'
    )
    
    parser.add_argument(
        '-t', '--timeout',
        dest='timeout',
        type=int,
        default=300,
        help='超时时间(秒) (默认: 300)'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细输出'
    )
    
    parser.add_argument(
        '--json',
        action='store_true',
        help='以JSON格式输出结果'
    )
    
    return parser.parse_args()


def parse_command_string(command_str):
    """解析命令字符串"""
    parts = command_str.strip().split()
    if not parts:
        return None, []

    command = parts[0]
    args = parts[1:] if len(parts) > 1 else []

    return command, args


def read_commands_from_file(file_path):
    """从文件读取命令列表"""
    commands = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()

                # 跳过空行和注释行
                if not line or line.startswith('#'):
                    continue

                command, args = parse_command_string(line)
                if command:
                    commands.append((command, args))
                else:
                    print(f"警告: 第{line_num}行格式错误: {line}")

    except FileNotFoundError:
        print(f"错误: 找不到命令文件: {file_path}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 读取命令文件时发生错误: {e}")
        sys.exit(1)

    return commands


def print_verbose_info(scripts, log_dir, timeout):
    """打印详细信息"""
    print("=== 并行Python脚本执行器 ===")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"日志目录: {log_dir}")
    print(f"超时时间: {timeout}秒")
    print(f"脚本数量: {len(scripts)}")
    print()
    
    print("准备执行的脚本:")
    for i, (script, args) in enumerate(scripts, 1):
        print(f"  {i}. python {script} {' '.join(args)}")
    print()


def print_result_summary(result, verbose=False):
    """打印结果摘要"""
    if verbose:
        print("\n" + "="*50)
        print("执行完成! 结果汇总:")
        print("="*50)
    
    success_icon = "✅" if result['success'] else "❌"
    print(f"{success_icon} 总体结果: {'成功' if result['success'] else '失败'}")
    print(f"📊 统计: {result['success_count']}/{result['total_scripts']} 成功")
    print(f"⏱️  耗时: {result['total_time']:.1f}秒")
    
    if result['timeout_occurred']:
        print("⚠️  执行超时!")
    
    if verbose:
        print(f"📁 日志目录: {result['log_directory']}")
        
        if result['failed_count'] > 0:
            print(f"\n❌ 失败的脚本 ({result['failed_count']}):")
            for thread_id, res in result['results'].items():
                if not res['success']:
                    print(f"  • {thread_id}: {res['command']}")
                    if 'error' in res:
                        print(f"    错误: {res['error']}")


def main():
    """主函数"""
    args = parse_arguments()
    
    # 准备脚本列表
    if args.scripts:
        # 从命令行参数解析脚本
        scripts = []
        for script_cmd in args.scripts:
            script_path, script_args = parse_script_command(script_cmd)
            if script_path:
                scripts.append((script_path, script_args))
            else:
                print(f"错误: 无效的脚本命令: {script_cmd}")
                sys.exit(1)
    else:
        # 从文件读取脚本
        scripts = read_scripts_from_file(args.script_file)
    
    if not scripts:
        print("错误: 没有找到要执行的脚本")
        sys.exit(1)
    
    # 创建日志目录
    os.makedirs(args.log_dir, exist_ok=True)
    
    # 显示详细信息
    if args.verbose:
        print_verbose_info(scripts, args.log_dir, args.timeout)
    
    # 执行脚本
    try:
        result = execute_python_scripts_parallel(
            scripts=scripts,
            log_dir=args.log_dir,
            timeout=args.timeout
        )
        
        # 输出结果
        if args.json:
            # JSON格式输出
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            # 普通格式输出
            print_result_summary(result, args.verbose)
        
        # 设置退出码
        sys.exit(0 if result['success'] else 1)
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
        sys.exit(130)
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
