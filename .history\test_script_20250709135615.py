#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 用于演示并行执行器的功能
"""

import sys
import time
import random
from datetime import datetime


def main():
    """主函数"""
    script_name = sys.argv[0]
    args = sys.argv[1:] if len(sys.argv) > 1 else []
    
    print(f"脚本 {script_name} 开始执行")
    print(f"接收到的参数: {args}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 模拟一些工作
    work_time = random.randint(5, 15)  # 随机工作5-15秒
    print(f"模拟工作时间: {work_time}秒")
    
    for i in range(work_time):
        time.sleep(1)
        print(f"工作进度: {i+1}/{work_time}")
        
        # 模拟一些输出
        if i % 3 == 0:
            print(f"处理数据: {random.randint(1000, 9999)}")
    
    # 随机决定是否成功
    success = random.choice([True, True, True, False])  # 75%成功率
    
    if success:
        print("任务执行成功!")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sys.exit(0)
    else:
        print("任务执行失败!")
        print("错误: 模拟的随机错误")
        sys.exit(1)


if __name__ == "__main__":
    main()
