#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 用于演示并行执行器的功能
"""

import sys
import time
import random
from datetime import datetime


def main():
    """Main function"""
    script_name = sys.argv[0]
    args = sys.argv[1:] if len(sys.argv) > 1 else []

    print(f"Script {script_name} starting")
    print(f"Received args: {args}")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Simulate some work
    work_time = random.randint(5, 15)  # Random work 5-15 seconds
    print(f"Simulated work time: {work_time} seconds")

    for i in range(work_time):
        time.sleep(1)
        print(f"Work progress: {i+1}/{work_time}")

        # Simulate some output
        if i % 3 == 0:
            print(f"Processing data: {random.randint(1000, 9999)}")

    # Randomly decide success
    success = random.choice([True, True, True, False])  # 75% success rate

    if success:
        print("Task completed successfully!")
        print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sys.exit(0)
    else:
        print("Task failed!")
        print("Error: Simulated random error")
        sys.exit(1)


if __name__ == "__main__":
    main()
