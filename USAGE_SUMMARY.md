# 通用命令并行执行器 - 使用总结

## 🎯 核心改进

原来的工具只能执行Python脚本，现在已经升级为**通用命令并行执行器**，可以执行任意命令！

## 🚀 三种使用方式

### 1. 命令行工具（最简单）

```bash
# 执行任意命令
python parallel_runner.py -c "python script.py arg1" -c "ping -n 3 127.0.0.1" -c "echo Hello" -l ./logs -t 300 -v

# Python脚本简化语法（自动添加python前缀）
python parallel_runner.py -s "script.py arg1" -s "script.py arg2" -l ./logs -t 300 -v

# 从文件读取命令
python parallel_runner.py -f commands.txt -l ./logs -t 300 -v
```

### 2. Python代码调用（推荐）

```python
from parallel_python_executor import execute_commands_parallel

# 你的固件下载命令
commands = [
    ("python", ["psis_download_firmware_tool.py", "***************", "5", "1.1", "r12"]),
    ("python", ["psis_download_firmware_tool.py", "***************", "8", "1.1", "r12"]),
    ("python", ["psis_download_firmware_tool.py", "***************", "1", "1.1", "r12"])
]

result = execute_commands_parallel(commands, "./logs", 600)

if result['success']:
    print("所有命令执行成功!")
else:
    print(f"有 {result['failed_count']} 个命令失败")
```

### 3. 向后兼容的Python脚本方式

```python
from parallel_python_executor import execute_python_scripts_parallel

# 原来的方式仍然可用
scripts = [
    ("psis_download_firmware_tool.py", ["***************", "5", "1.1", "r12"]),
    ("psis_download_firmware_tool.py", ["***************", "8", "1.1", "r12"]),
    ("psis_download_firmware_tool.py", ["***************", "1", "1.1", "r12"])
]

result = execute_python_scripts_parallel(scripts, "./logs", 600)
```

## 📝 命令文件格式

创建 `commands.txt` 文件：
```
# 注释行以#开头
python psis_download_firmware_tool.py *************** 5 1.1 r12
python psis_download_firmware_tool.py *************** 8 1.1 r12
python psis_download_firmware_tool.py *************** 1 1.1 r12
ping -n 3 ***************
echo Firmware download completed
```

## 🎯 针对你的固件下载需求

### 方式1: 命令行执行
```bash
python parallel_runner.py \
  -c "python psis_download_firmware_tool.py *************** 5 1.1 r12" \
  -c "python psis_download_firmware_tool.py *************** 8 1.1 r12" \
  -c "python psis_download_firmware_tool.py *************** 1 1.1 r12" \
  -l ./firmware_logs -t 600 -v
```

### 方式2: Python脚本简化语法
```bash
python parallel_runner.py \
  -s "psis_download_firmware_tool.py *************** 5 1.1 r12" \
  -s "psis_download_firmware_tool.py *************** 8 1.1 r12" \
  -s "psis_download_firmware_tool.py *************** 1 1.1 r12" \
  -l ./firmware_logs -t 600 -v
```

### 方式3: 从文件读取
```bash
# 创建 firmware_commands.txt 文件，内容如上面的命令文件格式
python parallel_runner.py -f firmware_commands.txt -l ./firmware_logs -t 600 -v
```

## 🔧 高级功能

### 混合命令类型
```python
commands = [
    # 网络检查
    ("ping", ["-n", "2", "***************"]),
    ("ping", ["-n", "2", "***************"]),
    
    # 固件下载
    ("python", ["psis_download_firmware_tool.py", "***************", "5", "1.1", "r12"]),
    ("python", ["psis_download_firmware_tool.py", "***************", "1", "1.1", "r12"]),
    
    # 系统命令
    ("echo", ["Download batch completed"]),
    ("date", [])
]

result = execute_commands_parallel(commands, "./logs", 600)
```

### JSON输出格式
```bash
python parallel_runner.py -s "test_script.py task1" --json
```

## 📊 执行结果

所有方式都返回相同格式的结果：
```python
{
    'success': True/False,           # 是否所有命令都成功
    'total_scripts': 3,              # 总命令数
    'success_count': 2,              # 成功数
    'failed_count': 1,               # 失败数
    'total_time': 45.2,              # 总耗时（秒）
    'timeout_occurred': False,       # 是否超时
    'results': {...},                # 每个命令的详细结果
    'log_directory': './logs'        # 日志目录
}
```

## 📁 日志文件

- 每个命令的输出单独保存
- 文件名格式：`命令名_参数_时间戳.log`
- 包含执行时间、命令、输出、返回码等完整信息

## 🎉 主要优势

1. **通用性**: 不再局限于Python脚本，可以执行任意命令
2. **灵活性**: 支持多种输入方式（命令行、文件、代码调用）
3. **兼容性**: 原有的Python脚本调用方式仍然可用
4. **实用性**: 特别适合你的固件下载并行执行需求
5. **可扩展性**: 可以轻松添加网络检查、系统命令等

现在你可以用这个工具来并行执行任何命令，不仅仅是Python脚本！
