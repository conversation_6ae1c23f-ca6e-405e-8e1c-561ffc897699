执行时间: 2025-07-09 14:00:03
执行命令: python test_script.py task1 param1
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
<PERSON><PERSON><PERSON> test_script.py starting
Received args: ['task1', 'param1']
Start time: 2025-07-09 14:00:03
Simulated work time: 15 seconds
Work progress: 1/15
Processing data: 6995
Work progress: 2/15
Work progress: 3/15
Work progress: 4/15
Processing data: 4785
Work progress: 5/15
Work progress: 6/15
Work progress: 7/15
Processing data: 4528
Work progress: 8/15
Work progress: 9/15
Work progress: 10/15
Processing data: 2218
Work progress: 11/15
Work progress: 12/15
Work progress: 13/15
Processing data: 1988
Work progress: 14/15
Work progress: 15/15
Task completed successfully!
End time: 2025-07-09 14:00:18
--------------------------------------------------
执行结束时间: 2025-07-09 14:00:18
返回码: 0
