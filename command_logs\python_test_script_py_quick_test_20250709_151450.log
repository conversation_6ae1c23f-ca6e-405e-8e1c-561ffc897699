执行时间: 2025-07-09 15:14:50
执行命令: python test_script.py quick_test
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
<PERSON><PERSON><PERSON> test_script.py starting
Received args: ['quick_test']
Start time: 2025-07-09 15:14:50
Simulated work time: 6 seconds
Work progress: 1/6
Processing data: 5210
Work progress: 2/6
Work progress: 3/6
Work progress: 4/6
Processing data: 5632
Work progress: 5/6
Work progress: 6/6
Task failed!
Error: Simulated random error
--------------------------------------------------
执行结束时间: 2025-07-09 15:14:56
返回码: 1
