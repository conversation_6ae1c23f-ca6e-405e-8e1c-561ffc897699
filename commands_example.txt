# 命令列表示例
# 每行一个命令，支持注释行（以#开头）

# Python脚本命令（需要明确指定python）
python psis_download_firmware_tool.py *************** 5 1.1 r12
python psis_download_firmware_tool.py *************** 8 1.1 r12
python psis_download_firmware_tool.py *************** 1 1.1 r12

# 系统命令示例
ping -c 3 google.com
curl -I https://github.com
echo Hello from parallel executor

# 更多命令示例
# python test_script.py task1 param1 param2
# ls -la
# whoami
# date
# uname -a
