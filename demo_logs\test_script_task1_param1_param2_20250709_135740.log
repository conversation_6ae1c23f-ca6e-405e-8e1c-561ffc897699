执行时间: 2025-07-09 13:57:40
执行命令: python test_script.py task1 param1 param2
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
<PERSON><PERSON><PERSON> test_script.py starting
Received args: ['task1', 'param1', 'param2']
Start time: 2025-07-09 13:57:41
Simulated work time: 12 seconds
Work progress: 1/12
Processing data: 7984
Work progress: 2/12
Work progress: 3/12
Work progress: 4/12
Processing data: 8699
Work progress: 5/12
Work progress: 6/12
Work progress: 7/12
Processing data: 6694
Work progress: 8/12
Work progress: 9/12
Work progress: 10/12
Processing data: 1003
Work progress: 11/12
Work progress: 12/12
Task completed successfully!
End time: 2025-07-09 13:57:53
--------------------------------------------------
执行结束时间: 2025-07-09 13:57:53
返回码: 0
