执行时间: 2025-07-09 13:57:40
执行命令: python test_script.py task3
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
<PERSON><PERSON><PERSON> test_script.py starting
Received args: ['task3']
Start time: 2025-07-09 13:57:41
Simulated work time: 11 seconds
Work progress: 1/11
Processing data: 8786
Work progress: 2/11
Work progress: 3/11
Work progress: 4/11
Processing data: 6854
Work progress: 5/11
Work progress: 6/11
Work progress: 7/11
Processing data: 9094
Work progress: 8/11
Work progress: 9/11
Work progress: 10/11
Processing data: 8343
Work progress: 11/11
Task completed successfully!
End time: 2025-07-09 13:57:52
--------------------------------------------------
执行结束时间: 2025-07-09 13:57:52
返回码: 0
