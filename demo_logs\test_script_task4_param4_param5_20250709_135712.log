执行时间: 2025-07-09 13:57:12
执行命令: python test_script.py task4 param4 param5 param6
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
Traceback (most recent call last):
  File "C:\test_tools\multi_python_process\test_script.py", line 48, in <module>
    main()
  File "C:\test_tools\multi_python_process\test_script.py", line 18, in main
    print(f"\u811a\u672c {script_name} \u5f00\u59cb\u6267\u884c")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
UnicodeEncodeError: 'charmap' codec can't encode characters in position 0-1: character maps to <undefined>
--------------------------------------------------
执行结束时间: 2025-07-09 13:57:12
返回码: 1
