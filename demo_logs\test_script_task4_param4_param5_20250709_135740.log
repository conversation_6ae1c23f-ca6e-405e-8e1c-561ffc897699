执行时间: 2025-07-09 13:57:40
执行命令: python test_script.py task4 param4 param5 param6
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
<PERSON><PERSON><PERSON> test_script.py starting
Received args: ['task4', 'param4', 'param5', 'param6']
Start time: 2025-07-09 13:57:41
Simulated work time: 12 seconds
Work progress: 1/12
Processing data: 3095
Work progress: 2/12
Work progress: 3/12
Work progress: 4/12
Processing data: 1608
Work progress: 5/12
Work progress: 6/12
Work progress: 7/12
Processing data: 8996
Work progress: 8/12
Work progress: 9/12
Work progress: 10/12
Processing data: 2795
Work progress: 11/12
Work progress: 12/12
Task failed!
Error: Simulated random error
--------------------------------------------------
执行结束时间: 2025-07-09 13:57:53
返回码: 1
