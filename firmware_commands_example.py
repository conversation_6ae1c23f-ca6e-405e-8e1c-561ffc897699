#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
固件下载命令并行执行示例
展示如何使用新的通用命令执行器来并行执行固件下载任务
"""

from parallel_python_executor import execute_commands_parallel
import os
from datetime import datetime


def main():
    """主函数 - 使用通用命令执行器执行固件下载任务"""
    
    print("=== 固件下载命令并行执行器 ===")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 定义要并行执行的固件下载命令
    # 现在使用完整的命令格式，第一个参数是命令，后面是参数列表
    firmware_commands = [
        ("python", ["psis_download_firmware_tool.py", "100.120.162.113", "5", "1.1", "r12"]),
        ("python", ["psis_download_firmware_tool.py", "100.120.162.113", "8", "1.1", "r12"]),
        ("python", ["psis_download_firmware_tool.py", "100.120.162.106", "1", "1.1", "r12"])
    ]
    
    # 设置日志目录
    log_dir = f"./firmware_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 设置超时时间（秒）- 根据固件下载的实际需要调整
    timeout_seconds = 600  # 10分钟
    
    print("准备并行执行以下固件下载命令:")
    for i, (command, args) in enumerate(firmware_commands, 1):
        print(f"  {i}. {command} {' '.join(args)}")
    
    print(f"\n日志目录: {log_dir}")
    print(f"超时时间: {timeout_seconds}秒 ({timeout_seconds//60}分钟)")
    print("\n开始执行固件下载任务...\n")
    
    # 执行并行下载
    result = execute_commands_parallel(
        commands=firmware_commands,
        log_dir=log_dir,
        timeout=timeout_seconds
    )
    
    # 显示执行结果
    print_firmware_result(result)
    
    return result


def mixed_commands_example():
    """混合命令类型示例 - 固件下载 + 网络检查"""
    
    print("=== 混合命令执行示例 ===")
    print("固件下载 + 网络连接检查")
    print()
    
    # 混合命令：固件下载 + 网络检查
    mixed_commands = [
        # 固件下载命令
        ("python", ["psis_download_firmware_tool.py", "100.120.162.113", "5", "1.1", "r12"]),
        ("python", ["psis_download_firmware_tool.py", "100.120.162.113", "8", "1.1", "r12"]),
        
        # 网络连接检查
        ("ping", ["-n", "3", "100.120.162.113"]),
        ("ping", ["-n", "3", "100.120.162.106"]),
        
        # 系统信息
        ("echo", ["Starting firmware download batch"]),
        ("whoami", [])
    ]
    
    result = execute_commands_parallel(
        commands=mixed_commands,
        log_dir="./mixed_logs",
        timeout=300
    )
    
    print_firmware_result(result)
    return result


def batch_firmware_with_validation():
    """带验证的批量固件下载示例"""
    
    print("=== 带验证的批量固件下载 ===")
    print()
    
    # 第一批：网络连接验证
    validation_commands = [
        ("ping", ["-n", "2", "100.120.162.113"]),
        ("ping", ["-n", "2", "100.120.162.106"]),
        ("echo", ["Network validation completed"])
    ]
    
    print("第一步：验证网络连接...")
    validation_result = execute_commands_parallel(
        commands=validation_commands,
        log_dir="./validation_logs",
        timeout=60
    )
    
    if validation_result['success']:
        print("✅ 网络验证成功，开始固件下载...")
        
        # 第二批：固件下载
        download_commands = [
            ("python", ["psis_download_firmware_tool.py", "100.120.162.113", "5", "1.1", "r12"]),
            ("python", ["psis_download_firmware_tool.py", "100.120.162.113", "8", "1.1", "r12"]),
            ("python", ["psis_download_firmware_tool.py", "100.120.162.106", "1", "1.1", "r12"])
        ]
        
        download_result = execute_commands_parallel(
            commands=download_commands,
            log_dir="./download_logs",
            timeout=600
        )
        
        print_firmware_result(download_result)
        return download_result
    else:
        print("❌ 网络验证失败，跳过固件下载")
        return validation_result


def print_firmware_result(result):
    """打印固件下载结果"""
    print("\n" + "="*60)
    print("命令执行完成!")
    print("="*60)
    
    # 总体状态
    if result['success']:
        print("✅ 所有命令执行成功!")
    else:
        print("❌ 部分命令执行失败!")
    
    print(f"\n📊 执行统计:")
    print(f"   总命令数: {result['total_scripts']}")
    print(f"   成功数: {result['success_count']}")
    print(f"   失败数: {result['failed_count']}")
    print(f"   总耗时: {result['total_time']:.1f}秒 ({result['total_time']/60:.1f}分钟)")
    
    if result['timeout_occurred']:
        print(f"   ⚠️  执行超时!")
    
    print(f"   📁 日志目录: {result['log_directory']}")
    
    # 详细结果
    print(f"\n📋 详细结果:")
    print("-" * 40)
    
    for thread_id, res in result['results'].items():
        status_icon = "✅" if res['success'] else "❌"
        status_text = "成功" if res['success'] else "失败"
        
        print(f"{status_icon} {thread_id}: {status_text}")
        print(f"   命令: {res['command']}")
        print(f"   返回码: {res['return_code']}")
        print(f"   日志: {os.path.basename(res['log_file'])}")
        
        if 'error' in res:
            print(f"   错误: {res['error']}")
        print()


def command_line_usage_examples():
    """命令行使用示例"""
    print("=== 命令行使用示例 ===")
    print()
    
    examples = [
        "# 直接指定命令（推荐用法）",
        'python parallel_runner.py -c "python psis_download_firmware_tool.py 100.120.162.113 5 1.1 r12" -c "python psis_download_firmware_tool.py 100.120.162.113 8 1.1 r12" -l ./logs -t 600 -v',
        "",
        "# 使用Python脚本简化语法",
        'python parallel_runner.py -s "psis_download_firmware_tool.py 100.120.162.113 5 1.1 r12" -s "psis_download_firmware_tool.py 100.120.162.113 8 1.1 r12" -l ./logs -t 600 -v',
        "",
        "# 从文件读取命令",
        'python parallel_runner.py -f firmware_commands.txt -l ./logs -t 600 -v',
        "",
        "# 混合命令类型",
        'python parallel_runner.py -c "ping -n 3 100.120.162.113" -s "psis_download_firmware_tool.py 100.120.162.113 5 1.1 r12" -l ./logs -t 300 -v'
    ]
    
    for example in examples:
        print(example)


if __name__ == "__main__":
    try:
        # 显示命令行使用示例
        command_line_usage_examples()
        print("\n" + "="*60 + "\n")
        
        # 执行主要的固件下载任务
        result = main()
        
        # 根据结果决定退出码
        exit_code = 0 if result['success'] else 1
        exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n\n用户中断执行")
        exit(130)
    except Exception as e:
        print(f"\n执行过程中发生错误: {e}")
        exit(1)
