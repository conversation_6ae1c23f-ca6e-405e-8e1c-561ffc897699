#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
固件下载脚本并行执行示例
专门针对 psis_download_firmware_tool.py 脚本的并行执行
"""

from parallel_python_executor import execute_python_scripts_parallel
import os
from datetime import datetime


def main():
    """主函数 - 执行固件下载任务"""
    
    print("=== 固件下载脚本并行执行器 ===")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 定义要并行执行的固件下载命令
    # 格式: (脚本路径, [IP地址, 端口, 版本, 其他参数])
    firmware_scripts = [
        ("psis_download_firmware_tool.py", ["***************", "5", "1.1", "r12"]),
        ("psis_download_firmware_tool.py", ["***************", "8", "1.1", "r12"]),
        ("psis_download_firmware_tool.py", ["***************", "1", "1.1", "r12"])
    ]
    
    # 设置日志目录
    log_dir = f"./firmware_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 设置超时时间（秒）- 根据固件下载的实际需要调整
    timeout_seconds = 600  # 10分钟
    
    print("准备并行执行以下固件下载任务:")
    for i, (script, args) in enumerate(firmware_scripts, 1):
        print(f"  {i}. python {script} {' '.join(args)}")
    
    print(f"\n日志目录: {log_dir}")
    print(f"超时时间: {timeout_seconds}秒 ({timeout_seconds//60}分钟)")
    print("\n开始执行固件下载任务...\n")
    
    # 执行并行下载
    result = execute_python_scripts_parallel(
        scripts=firmware_scripts,
        log_dir=log_dir,
        timeout=timeout_seconds
    )
    
    # 显示执行结果
    print_firmware_result(result)
    
    return result


def print_firmware_result(result):
    """打印固件下载结果"""
    print("\n" + "="*60)
    print("固件下载任务执行完成!")
    print("="*60)
    
    # 总体状态
    if result['success']:
        print("✅ 所有固件下载任务执行成功!")
    else:
        print("❌ 部分固件下载任务执行失败!")
    
    print(f"\n📊 执行统计:")
    print(f"   总任务数: {result['total_scripts']}")
    print(f"   成功数: {result['success_count']}")
    print(f"   失败数: {result['failed_count']}")
    print(f"   总耗时: {result['total_time']:.1f}秒 ({result['total_time']/60:.1f}分钟)")
    
    if result['timeout_occurred']:
        print(f"   ⚠️  执行超时!")
    
    print(f"   📁 日志目录: {result['log_directory']}")
    
    # 详细结果
    print(f"\n📋 详细结果:")
    print("-" * 40)
    
    for thread_id, res in result['results'].items():
        # 解析命令获取IP和端口信息
        cmd_parts = res['command'].split()
        if len(cmd_parts) >= 4:
            ip = cmd_parts[2]
            port = cmd_parts[3]
            task_info = f"{ip}:{port}"
        else:
            task_info = "未知任务"
        
        status_icon = "✅" if res['success'] else "❌"
        status_text = "成功" if res['success'] else "失败"
        
        print(f"{status_icon} {thread_id} ({task_info}): {status_text}")
        print(f"   命令: {res['command']}")
        print(f"   返回码: {res['return_code']}")
        print(f"   日志: {os.path.basename(res['log_file'])}")
        
        if 'error' in res:
            print(f"   错误: {res['error']}")
        print()
    
    # 给出后续建议
    if result['failed_count'] > 0:
        print("💡 建议:")
        print("   1. 查看失败任务的日志文件了解具体错误原因")
        print("   2. 检查网络连接和目标设备状态")
        print("   3. 确认固件下载脚本的参数是否正确")
        print("   4. 可以单独重新执行失败的任务")
    else:
        print("🎉 所有固件下载任务都已成功完成!")


def create_custom_firmware_task():
    """创建自定义固件下载任务的示例"""
    
    # 可以根据需要动态生成任务列表
    ip_list = [
        ("***************", "5"),
        ("***************", "8"), 
        ("***************", "1"),
        # 可以添加更多IP和端口组合
    ]
    
    firmware_version = "1.1"
    firmware_type = "r12"
    
    # 生成脚本列表
    scripts = []
    for ip, port in ip_list:
        scripts.append((
            "psis_download_firmware_tool.py", 
            [ip, port, firmware_version, firmware_type]
        ))
    
    return scripts


def batch_firmware_download():
    """批量固件下载的高级示例"""
    
    # 不同批次的固件下载任务
    batches = {
        "batch_1": [
            ("psis_download_firmware_tool.py", ["***************", "5", "1.1", "r12"]),
            ("psis_download_firmware_tool.py", ["***************", "8", "1.1", "r12"]),
        ],
        "batch_2": [
            ("psis_download_firmware_tool.py", ["***************", "1", "1.1", "r12"]),
            ("psis_download_firmware_tool.py", ["***************", "2", "1.1", "r12"]),
        ]
    }
    
    all_results = {}
    
    for batch_name, scripts in batches.items():
        print(f"\n执行批次: {batch_name}")
        print("-" * 30)
        
        result = execute_python_scripts_parallel(
            scripts=scripts,
            log_dir=f"./logs/{batch_name}",
            timeout=300
        )
        
        all_results[batch_name] = result
        print_firmware_result(result)
        
        # 如果当前批次失败，询问是否继续
        if not result['success']:
            print(f"\n批次 {batch_name} 执行失败，是否继续下一批次？")
            # 在实际使用中可以添加用户交互
    
    return all_results


if __name__ == "__main__":
    try:
        # 执行主要的固件下载任务
        result = main()
        
        # 根据结果决定退出码
        exit_code = 0 if result['success'] else 1
        exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n\n用户中断执行")
        exit(130)
    except Exception as e:
        print(f"\n执行过程中发生错误: {e}")
        exit(1)
