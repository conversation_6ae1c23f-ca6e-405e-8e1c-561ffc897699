执行时间: 2025-07-09 14:00:26
执行命令: python test_script.py quick_test
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
<PERSON><PERSON><PERSON> test_script.py starting
Received args: ['quick_test']
Start time: 2025-07-09 14:00:26
Simulated work time: 11 seconds
Work progress: 1/11
Processing data: 8013
Work progress: 2/11
Work progress: 3/11
Work progress: 4/11
Processing data: 2657
Work progress: 5/11
Work progress: 6/11
Work progress: 7/11
Processing data: 2763
Work progress: 8/11
Work progress: 9/11
Work progress: 10/11
Processing data: 4295
Work progress: 11/11
Task completed successfully!
End time: 2025-07-09 14:00:37
--------------------------------------------------
执行结束时间: 2025-07-09 14:00:37
返回码: 0
