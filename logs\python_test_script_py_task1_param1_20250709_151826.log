执行时间: 2025-07-09 15:18:26
执行命令: python test_script.py task1 param1
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
<PERSON><PERSON><PERSON> test_script.py starting
Received args: ['task1', 'param1']
Start time: 2025-07-09 15:18:26
Simulated work time: 13 seconds
Work progress: 1/13
Processing data: 6764
Work progress: 2/13
Work progress: 3/13
Work progress: 4/13
Processing data: 1687
Work progress: 5/13
Work progress: 6/13
Work progress: 7/13
Processing data: 6831
Work progress: 8/13
Work progress: 9/13
Work progress: 10/13
Processing data: 9300
Work progress: 11/13
Work progress: 12/13
Work progress: 13/13
Processing data: 2644
Task completed successfully!
End time: 2025-07-09 15:18:39
--------------------------------------------------
执行结束时间: 2025-07-09 15:18:39
返回码: 0
