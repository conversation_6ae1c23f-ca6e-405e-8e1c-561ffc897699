执行时间: 2025-07-09 15:18:26
执行命令: python test_script.py task2 param2
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
<PERSON><PERSON><PERSON> test_script.py starting
Received args: ['task2', 'param2']
Start time: 2025-07-09 15:18:26
Simulated work time: 9 seconds
Work progress: 1/9
Processing data: 4764
Work progress: 2/9
Work progress: 3/9
Work progress: 4/9
Processing data: 1069
Work progress: 5/9
Work progress: 6/9
Work progress: 7/9
Processing data: 3502
Work progress: 8/9
Work progress: 9/9
Task completed successfully!
End time: 2025-07-09 15:18:35
--------------------------------------------------
执行结束时间: 2025-07-09 15:18:35
返回码: 0
