执行时间: 2025-07-09 15:15:04
执行命令: python test_script.py task1
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
<PERSON><PERSON><PERSON> test_script.py starting
Received args: ['task1']
Start time: 2025-07-09 15:15:04
Simulated work time: 10 seconds
Work progress: 1/10
Processing data: 4753
Work progress: 2/10
Work progress: 3/10
Work progress: 4/10
Processing data: 8139
Work progress: 5/10
Work progress: 6/10
Work progress: 7/10
Processing data: 8804
Work progress: 8/10
Work progress: 9/10
Work progress: 10/10
Processing data: 3318
Task completed successfully!
End time: 2025-07-09 15:15:15
--------------------------------------------------
执行结束时间: 2025-07-09 15:15:15
返回码: 0
