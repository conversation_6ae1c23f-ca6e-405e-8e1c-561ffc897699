执行时间: 2025-07-09 15:15:04
执行命令: python test_script.py task2
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
<PERSON><PERSON><PERSON> test_script.py starting
Received args: ['task2']
Start time: 2025-07-09 15:15:04
Simulated work time: 8 seconds
Work progress: 1/8
Processing data: 1013
Work progress: 2/8
Work progress: 3/8
Work progress: 4/8
Processing data: 8353
Work progress: 5/8
Work progress: 6/8
Work progress: 7/8
Processing data: 7876
Work progress: 8/8
Task failed!
Error: Simulated random error
--------------------------------------------------
执行结束时间: 2025-07-09 15:15:13
返回码: 1
