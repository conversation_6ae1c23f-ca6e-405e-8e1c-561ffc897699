# 固件下载脚本列表示例
# 每行一个脚本命令，支持注释行（以#开头）

# 第一组固件下载任务
psis_download_firmware_tool.py *************** 5 1.1 r12
psis_download_firmware_tool.py *************** 8 1.1 r12
psis_download_firmware_tool.py *************** 1 1.1 r12

# 第二组固件下载任务（如果需要）
# psis_download_firmware_tool.py *************** 2 1.1 r12
# psis_download_firmware_tool.py *************** 3 1.1 r12

# 其他类型的脚本示例
# test_script.py task1 param1 param2
# test_script.py task2 param3
