执行时间: 2025-07-09 15:16:05
执行命令: python test_script.py task1 param1
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
<PERSON><PERSON><PERSON> test_script.py starting
Received args: ['task1', 'param1']
Start time: 2025-07-09 15:16:05
Simulated work time: 7 seconds
Work progress: 1/7
Processing data: 6375
Work progress: 2/7
Work progress: 3/7
Work progress: 4/7
Processing data: 1241
Work progress: 5/7
Work progress: 6/7
Work progress: 7/7
Processing data: 4044
Task completed successfully!
End time: 2025-07-09 15:16:12
--------------------------------------------------
执行结束时间: 2025-07-09 15:16:12
返回码: 0
