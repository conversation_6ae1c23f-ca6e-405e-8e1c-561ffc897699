执行时间: 2025-07-09 15:16:05
执行命令: python test_script.py task2 param2
工作目录: C:\test_tools\multi_python_process
--------------------------------------------------
<PERSON><PERSON><PERSON> test_script.py starting
Received args: ['task2', 'param2']
Start time: 2025-07-09 15:16:05
Simulated work time: 5 seconds
Work progress: 1/5
Processing data: 6296
Work progress: 2/5
Work progress: 3/5
Work progress: 4/5
Processing data: 1811
Work progress: 5/5
Task completed successfully!
End time: 2025-07-09 15:16:10
--------------------------------------------------
执行结束时间: 2025-07-09 15:16:10
返回码: 0
